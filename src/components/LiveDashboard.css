/* Enhanced <PERSON><PERSON><PERSON> Dashboard - Professional Hackathon UI */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

.live-dashboard {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
    position: relative;
    overflow: hidden;
}

.live-dashboard::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

/* Enhanced Header */
.dashboard-header {
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(20px);
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(120, 219, 255, 0.2);
    position: relative;
    z-index: 1000;
}

.dashboard-header h1 {
    margin: 0;
    font-size: 2.2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #78dbff 0%, #ff77c6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(120, 219, 255, 0.5);
    letter-spacing: -0.5px;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1.2rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    font-weight: 600;
    font-size: 0.95rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    border: 1px solid rgba(120, 219, 255, 0.3);
}

.status-dot {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    animation: statusPulse 2s infinite;
    box-shadow: 0 0 10px currentColor;
}

.status-dot.live {
    background: #00ff88;
    color: #00ff88;
}

.status-dot.offline {
    background: #ff6b6b;
    color: #ff6b6b;
    animation: none;
}

@keyframes statusPulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.toggle-btn {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(120, 219, 255, 0.3);
    color: white;
    padding: 0.7rem 1.2rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.toggle-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.toggle-btn:hover::before {
    left: 100%;
}

.toggle-btn:hover {
    background: rgba(120, 219, 255, 0.15);
    border-color: rgba(120, 219, 255, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(120, 219, 255, 0.3);
}

.toggle-btn.active {
    background: linear-gradient(135deg, #78dbff 0%, #4facfe 100%);
    border-color: #78dbff;
    box-shadow: 0 0 30px rgba(120, 219, 255, 0.6);
    color: #0f0f23;
    font-weight: 600;
}

/* Enhanced Main Content */
.dashboard-content {
    flex: 1;
    display: grid;
    grid-template-columns: 320px 1fr 340px;
    gap: 0;
    overflow: hidden;
    position: relative;
    z-index: 1;
}

/* Enhanced Sidebars */
.dashboard-sidebar,
.alerts-sidebar {
    background: rgba(15, 15, 35, 0.8);
    backdrop-filter: blur(20px);
    padding: 1.5rem;
    overflow-y: auto;
    border-right: 1px solid rgba(120, 219, 255, 0.2);
    scrollbar-width: thin;
    scrollbar-color: rgba(120, 219, 255, 0.3) transparent;
}

.dashboard-sidebar::-webkit-scrollbar,
.alerts-sidebar::-webkit-scrollbar {
    width: 6px;
}

.dashboard-sidebar::-webkit-scrollbar-track,
.alerts-sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.dashboard-sidebar::-webkit-scrollbar-thumb,
.alerts-sidebar::-webkit-scrollbar-thumb {
    background: rgba(120, 219, 255, 0.3);
    border-radius: 3px;
}

.alerts-sidebar {
    border-right: none;
    border-left: 1px solid rgba(120, 219, 255, 0.2);
}

/* Emergency Floating Action Button */
.emergency-fab {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: emergencyPulse 3s infinite;
}

.emergency-fab:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
}

@keyframes emergencyPulse {
    0%, 100% {
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    }
    50% {
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.8), 0 0 0 10px rgba(255, 107, 107, 0.1);
    }
}

/* Enhanced Panel Styles */
.info-panel,
.stats-panel,
.controls-panel,
.zones-panel,
.test-panel,
.alerts-panel,
.responders-panel,
.zone-details-panel {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(15px);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(120, 219, 255, 0.2);
    transition: all 0.3s ease;
}

.info-panel:hover,
.stats-panel:hover,
.controls-panel:hover,
.zones-panel:hover,
.test-panel:hover,
.alerts-panel:hover,
.responders-panel:hover,
.zone-details-panel:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(120, 219, 255, 0.4);
    transform: translateY(-2px);
}

/* AI Detection Status Styles */
.stat-source {
    color: #00ff88;
    font-size: 0.75rem;
    margin-left: 0.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* AI Status Indicator */
.ai-status-indicator {
    position: fixed;
    top: 90px;
    right: 30px;
    background: rgba(15, 15, 35, 0.9);
    backdrop-filter: blur(15px);
    padding: 12px 20px;
    border-radius: 25px;
    border: 1px solid rgba(120, 219, 255, 0.3);
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    font-weight: 500;
    z-index: 999;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.ai-status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: statusPulse 2s infinite;
}

.ai-status-dot.online {
    background: #00ff88;
    box-shadow: 0 0 10px #00ff88;
}

.ai-status-dot.training {
    background: #ffa500;
    box-shadow: 0 0 10px #ffa500;
}

.ai-status-dot.offline {
    background: #ff6b6b;
    box-shadow: 0 0 10px #ff6b6b;
    animation: none;
}

.ai-status-dot.deployed {
    background: #78dbff;
    box-shadow: 0 0 10px #78dbff;
}

/* AI Intelligence Panel */
.ai-intelligence-panel {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(15px);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(120, 219, 255, 0.2);
    transition: all 0.3s ease;
}

.ai-intelligence-panel h3 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #78dbff;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ai-intelligence-panel h4 {
    margin: 1rem 0 0.5rem 0;
    font-size: 0.95rem;
    font-weight: 600;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ai-status {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.threat-low {
    color: #00ff88 !important;
}

.threat-medium {
    color: #ffa500 !important;
}

.threat-high {
    color: #ff6b6b !important;
}

.threat-critical {
    color: #ff0000 !important;
    animation: threatPulse 1s infinite;
}

@keyframes threatPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.ai-services-health {
    margin-top: 1rem;
}

.service-status-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(120, 219, 255, 0.1);
}

.service-name {
    font-size: 0.8rem;
    color: #cccccc;
}

.service-status {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    padding: 2px 6px;
    border-radius: 4px;
}

.service-status.online {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
    border: 1px solid #00ff88;
}

.service-status.offline {
    background: rgba(255, 107, 107, 0.2);
    color: #ff6b6b;
    border: 1px solid #ff6b6b;
}

.service-status.error {
    background: rgba(255, 0, 0, 0.2);
    color: #ff0000;
    border: 1px solid #ff0000;
}

.ai-alerts-section {
    margin-top: 1rem;
}

.ai-alerts-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.ai-alert-item {
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border-left: 4px solid;
}

.ai-alert-item.low {
    border-left-color: #00ff88;
}

.ai-alert-item.medium {
    border-left-color: #ffa500;
}

.ai-alert-item.high {
    border-left-color: #ff6b6b;
}

.ai-alert-item.critical {
    border-left-color: #ff0000;
    animation: alertPulse 2s infinite;
}

@keyframes alertPulse {
    0%, 100% {
        background: rgba(255, 255, 255, 0.05);
    }
    50% {
        background: rgba(255, 0, 0, 0.1);
    }
}

.alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.alert-type {
    font-size: 0.8rem;
    font-weight: 600;
    color: #78dbff;
}

.alert-confidence {
    font-size: 0.75rem;
    color: #00ff88;
    font-weight: 600;
}

.alert-message {
    font-size: 0.8rem;
    color: #ffffff;
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.alert-source {
    font-size: 0.7rem;
    color: #888888;
    font-style: italic;
}

.data-controls {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
    transform: scale(1.1);
}

.ai-status {
    padding: 0.5rem;
    background: rgba(40, 167, 69, 0.1);
    border-radius: 4px;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.ai-indicator {
    color: #28a745;
    font-size: 0.8rem;
    font-weight: 600;
    animation: pulse 2s infinite;
}

.alerts-sidebar {
    border-right: none;
    border-left: 1px solid #444;
}

/* Panels */
.info-panel,
.stats-panel,
.controls-panel,
.zones-panel,
.test-panel,
.alerts-panel,
.responders-panel,
.zone-details-panel {
    background: #333;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #444;
}

.info-panel h3,
.stats-panel h3,
.controls-panel h3,
.zones-panel h3,
.test-panel h3,
.alerts-panel h3,
.responders-panel h3,
.zone-details-panel h3 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    color: #ffffff;
    border-bottom: 1px solid #555;
    padding-bottom: 0.5rem;
}

/* Statistics */
.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

.stat-label {
    color: #ccc;
    font-size: 0.9rem;
}

.stat-value {
    font-weight: 600;
    color: #fff;
}

.stat-value.critical {
    color: #ff4444;
}

.stat-value.warning {
    color: #ffaa44;
}

/* Controls */
.control-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    cursor: pointer;
    font-size: 0.9rem;
}

.control-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

/* Zones List */
.zones-list {
    max-height: 300px;
    overflow-y: auto;
}

.zone-item {
    background: #444;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.zone-item:hover {
    background: #555;
}

.zone-item.critical {
    border-left-color: #ff4444;
}

.zone-item.warning {
    border-left-color: #ffaa44;
}

.zone-item.normal {
    border-left-color: #44aa44;
}

.zone-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.zone-stats {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #ccc;
}

.alert-level {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
}

.alert-level.critical {
    background: #ff4444;
    color: white;
}

.alert-level.warning {
    background: #ffaa44;
    color: white;
}

.alert-level.normal {
    background: #44aa44;
    color: white;
}

/* Test Controls */
.test-btn {
    background: #667eea;
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    width: 100%;
    transition: background 0.3s ease;
}

.test-btn:hover {
    background: #5a6fd8;
}

/* Map Container */
.map-container {
    background: #000;
    position: relative;
    overflow: hidden;
}

/* Alerts */
.alerts-list,
.responders-list {
    max-height: 400px;
    overflow-y: auto;
}

.alert-item {
    background: #444;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-left: 4px solid transparent;
}

.alert-item.high {
    border-left-color: #ff4444;
}

.alert-item.medium {
    border-left-color: #ffaa44;
}

.alert-item.low {
    border-left-color: #44aa44;
}

.alert-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
}

.alert-type {
    font-weight: 600;
    font-size: 0.9rem;
}

.alert-time {
    font-size: 0.8rem;
    color: #ccc;
}

.alert-zone {
    font-size: 0.8rem;
    color: #aaa;
    margin-bottom: 0.25rem;
}

.alert-message {
    font-size: 0.85rem;
    line-height: 1.3;
}

/* Responders */
.responder-item {
    background: #444;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

.responder-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
}

.responder-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.responder-status {
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.responder-status.active {
    background: #44aa44;
    color: white;
}

.responder-status.responding {
    background: #ffaa44;
    color: white;
}

.responder-status.standby {
    background: #666;
    color: white;
}

.responder-status.patrol {
    background: #667eea;
    color: white;
}

.responder-type {
    font-size: 0.8rem;
    color: #aaa;
    margin-bottom: 0.25rem;
}

.responder-time {
    font-size: 0.75rem;
    color: #888;
}

/* Zone Details */
.zone-details h4 {
    margin: 0 0 1rem 0;
    color: #fff;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

.close-details {
    background: #666;
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
    margin-top: 1rem;
}

.close-details:hover {
    background: #777;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-content {
        grid-template-columns: 250px 1fr 250px;
    }
}

@media (max-width: 768px) {
    .dashboard-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }

    .dashboard-sidebar,
    .alerts-sidebar {
        max-height: 200px;
        overflow-y: auto;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #333;
}

::-webkit-scrollbar-thumb {
    background: #666;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* Video Integration Styles */
.video-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.video-btn:hover {
    background: linear-gradient(135deg, #ff5252, #d63031);
}

.prediction-btn {
    background: linear-gradient(135deg, #6f42c1, #5a3a9c);
}

.prediction-btn:hover {
    background: linear-gradient(135deg, #5a3a9c, #4a307a);
}

.prediction-btn.active {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.3);
}

.map-video-split {
    display: flex;
    height: 100%;
    gap: 16px;
    padding: 16px;
    background: #1a1a1a;
}

.video-section {
    flex: 1;
    min-width: 400px;
    display: flex;
    flex-direction: column;
}

.map-section {
    flex: 1.5;
    min-width: 500px;
    display: flex;
    flex-direction: column;
}

.video-status-panel {
    background: #333;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #444;
}

.video-status {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-label {
    color: #ccc;
    font-size: 0.9rem;
}

.status-value {
    font-weight: 600;
    color: #fff;
    font-size: 0.9rem;
}

.status-value.processing {
    color: #28a745;
}

.status-value.stopped {
    color: #dc3545;
}

.status-value.initialized {
    color: #ffc107;
}

/* Responsive adjustments for video layout */
@media (max-width: 1024px) {
    .map-video-split {
        flex-direction: column;
        gap: 12px;
        padding: 12px;
    }

    .video-section,
    .map-section {
        min-width: auto;
        flex: none;
    }

    .video-section {
        order: 1;
    }

    .map-section {
        order: 2;
        height: 400px;
    }
}

@media (max-width: 768px) {
    .header-controls {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .map-video-split {
        padding: 8px;
        gap: 8px;
    }

    .video-btn,
    .prediction-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}

/* Prediction System Styles */
.dashboard-split {
    display: flex;
    height: 100%;
    gap: 16px;
    background: #1a1a1a;
}

.map-prediction-split {
    flex: 2;
    display: flex;
    flex-direction: column;
}

.prediction-section {
    flex: 1;
    min-width: 400px;
    max-width: 500px;
    overflow-y: auto;
}

.prediction-status-panel {
    background: #333;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #444;
}

.prediction-status {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.status-value.offline {
    color: #dc3545;
}

.status-value.initialized {
    color: #ffc107;
}

.status-value.training {
    color: #17a2b8;
}

.status-value.deployed {
    color: #28a745;
}

.status-value.critical {
    color: #dc3545;
}

.status-value.warning {
    color: #ffc107;
}

.status-value.caution {
    color: #fd7e14;
}

.status-value.normal {
    color: #28a745;
}

/* Responsive adjustments for prediction layout */
@media (max-width: 1200px) {
    .dashboard-split {
        flex-direction: column;
        gap: 12px;
    }

    .prediction-section {
        min-width: auto;
        max-width: none;
        flex: none;
        height: 400px;
    }
}

@media (max-width: 768px) {
    .dashboard-split {
        padding: 8px;
        gap: 8px;
    }

    .prediction-section {
        height: 300px;
    }

    .prediction-btn {
        font-size: 0.7rem;
        padding: 0.3rem 0.6rem;
    }
}
