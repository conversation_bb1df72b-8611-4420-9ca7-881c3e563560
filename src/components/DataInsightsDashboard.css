/* Data Insights Dashboard Styles */
.data-insights-dashboard {
  background: rgba(15, 15, 35, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 2rem;
  margin: 1rem;
  border: 1px solid rgba(120, 219, 255, 0.2);
  color: #ffffff;
  font-family: 'Inter', sans-serif;
  max-height: 90vh;
  overflow-y: auto;
}

.insights-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(120, 219, 255, 0.2);
}

.insights-header h2 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #78dbff 0%, #ff77c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.insights-controls {
  display: flex;
  gap: 1rem;
}

.timeframe-selector,
.metric-selector {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(120, 219, 255, 0.3);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.timeframe-selector:hover,
.metric-selector:hover {
  background: rgba(120, 219, 255, 0.15);
  border-color: rgba(120, 219, 255, 0.6);
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.insight-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(15px);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(120, 219, 255, 0.2);
  transition: all 0.3s ease;
}

.insight-card:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(120, 219, 255, 0.4);
  transform: translateY(-2px);
}

.insight-card h3 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #78dbff;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.insight-card h4 {
  margin: 1rem 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
}

/* Safety Metrics */
.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(120, 219, 255, 0.1);
}

.metric-label {
  font-size: 0.8rem;
  color: #cccccc;
  margin-bottom: 0.5rem;
  text-align: center;
}

.metric-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: #ffffff;
}

.metric-value.score-low { color: #00ff88; }
.metric-value.score-medium { color: #ffa500; }
.metric-value.score-high { color: #ff6b6b; }
.metric-value.score-critical { color: #ff0000; }

.metric-value.risk-low { color: #00ff88; }
.metric-value.risk-medium { color: #ffa500; }
.metric-value.risk-high { color: #ff6b6b; }
.metric-value.risk-critical { color: #ff0000; }

/* Risk Assessment */
.risk-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.risk-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.risk-label {
  font-size: 0.9rem;
  color: #cccccc;
}

.risk-value {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
}

.risk-bar {
  position: relative;
  width: 120px;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
}

.risk-fill {
  height: 100%;
  background: linear-gradient(90deg, #00ff88 0%, #ffa500 50%, #ff6b6b 100%);
  transition: width 0.3s ease;
}

.risk-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.8rem;
  font-weight: 600;
  color: #000;
}

/* Impact Analysis */
.impact-metrics {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.impact-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.impact-label {
  font-size: 0.9rem;
  color: #cccccc;
}

.impact-value {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
}

.impact-value.danger {
  color: #ff6b6b;
}

.impact-value.success {
  color: #00ff88;
}

/* Real-World Validation */
.validation-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.global-stats,
.drishti-impact {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(120, 219, 255, 0.1);
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.stat-value {
  font-weight: 600;
  color: #ffffff;
}

.stat-value.danger {
  color: #ff6b6b;
}

.stat-value.success {
  color: #00ff88;
}

/* Historical Comparison */
.trend-analysis {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.trend-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.trend-label {
  font-size: 0.9rem;
  color: #cccccc;
}

.trend-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.trend-bars {
  display: flex;
  align-items: end;
  gap: 1rem;
  height: 100px;
}

.trend-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: end;
  width: 60px;
  background: rgba(120, 219, 255, 0.3);
  border-radius: 4px 4px 0 0;
  position: relative;
  transition: all 0.3s ease;
}

.trend-bar.current {
  background: rgba(120, 219, 255, 0.6);
}

.trend-value {
  position: absolute;
  top: -25px;
  font-size: 0.8rem;
  font-weight: 600;
  color: #78dbff;
}

.trend-period {
  position: absolute;
  bottom: -20px;
  font-size: 0.7rem;
  color: #cccccc;
  text-align: center;
  width: 100%;
}

.trend-indicator {
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.trend-indicator.increasing {
  color: #ff6b6b;
}

.trend-indicator.decreasing {
  color: #00ff88;
}

.trend-indicator.stable {
  color: #ffa500;
}

/* AI Effectiveness */
.effectiveness-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.effectiveness-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(120, 219, 255, 0.1);
  text-align: center;
}

.effectiveness-label {
  font-size: 0.8rem;
  color: #cccccc;
  margin-bottom: 0.5rem;
}

.effectiveness-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #00ff88;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .insights-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid,
  .effectiveness-metrics {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .data-insights-dashboard {
    padding: 1rem;
    margin: 0.5rem;
  }
  
  .insights-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .insights-controls {
    justify-content: center;
  }
  
  .trend-bars {
    gap: 0.5rem;
  }
  
  .trend-bar {
    width: 40px;
  }
}

/* Scrollbar Styling */
.data-insights-dashboard::-webkit-scrollbar {
  width: 8px;
}

.data-insights-dashboard::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.data-insights-dashboard::-webkit-scrollbar-thumb {
  background: rgba(120, 219, 255, 0.3);
  border-radius: 4px;
}

.data-insights-dashboard::-webkit-scrollbar-thumb:hover {
  background: rgba(120, 219, 255, 0.5);
}
